# -*- coding: utf-8 -*-
"""
这是一个Python脚本，用于将organizations.xlsx文件的部门数据导入到MySQL数据库中。
脚本将读取organizations.xlsx文件，根据full_path列的层级关系（用>分隔）生成父子关系，
并按照指定规则生成code、order_no等字段，最终生成包含所有INSERT语句的SQL文件。

使用方法:
1. 确保您已安装 pandas 和 openpyxl 库。如果未安装，请在终端中运行:
   pip install pandas openpyxl
2. 将organizations.xlsx文件放在脚本同一目录下。
3. 在终端中运行此脚本:
   python importdept.py
4. 脚本运行完成后，会生成一个 'insert_data.sql' 文件。您可以使用MySQL客户端执行此文件。

生成规则:
- 第一层级code固定为DJDC，父级为ROOT
- 后续层级code按父级code + 序号（01、02...）规则生成
- order_no按同层级内的序号生成（1、2、3...）
- type=UM，source=UC，status=1
"""

import pandas as pd
import uuid
from datetime import datetime


def get_order_no(parent_path, dept_order_counters):
    """
    根据父级路径获取当前部门在同级中的排序号

    Args:
        parent_path (str): 父级路径，如果为None则表示根级别
        dept_order_counters (dict): 各父级下的子部门计数器

    Returns:
        int: 当前部门在同级中的排序号（从1开始）
    """
    # 如果父级路径为空，表示是根级别
    if parent_path is None or parent_path == 'nan':
        parent_key = 'ROOT'
    else:
        parent_key = parent_path

    # 获取当前父级下的子部门数量并递增
    if parent_key not in dept_order_counters:
        dept_order_counters[parent_key] = 1
    else:
        dept_order_counters[parent_key] += 1

    return dept_order_counters[parent_key]


def parse_parent_path_from_full_path(full_path):
    """
    从full_path中解析出parent_path

    Args:
        full_path (str): 完整路径，如"中国电建>华中区域>盛世江城"

    Returns:
        str or None: 父级路径，如"中国电建>华中区域"，如果是根级别则返回None
    """
    if not full_path or full_path.strip() == '':
        return None

    path_parts = full_path.split('>')
    if len(path_parts) <= 1:
        return None  # 根级别，没有父级

    # 返回除最后一部分外的所有部分
    return '>'.join(path_parts[:-1])


def generate_dept_code(parent_code, child_counter):
    """
    生成部门code

    Args:
        parent_code (str): 父级code，如果为None表示根级别
        child_counter (dict): 子部门计数器

    Returns:
        str: 生成的部门code
    """
    if parent_code is None or parent_code == 'ROOT':
        # 根级别，第一层级固定为DJDC
        return 'DJDC'

    # 获取当前父级下的子部门序号
    if parent_code not in child_counter:
        child_counter[parent_code] = 1
    else:
        child_counter[parent_code] += 1

    # 格式化为两位数
    child_num = str(child_counter[parent_code]).zfill(2)

    # 拼接生成新的code
    return f"{parent_code}{child_num}"


def create_missing_parent_dept(parent_path, dept_map, child_counter, dept_order_counters, sql_statements, table_name):
    """
    递归创建缺失的父级部门

    Args:
        parent_path (str): 需要创建的父级部门路径
        dept_map (dict): 部门映射字典
        child_counter (dict): 子部门code计数器
        dept_order_counters (dict): 子部门order_no计数器
        sql_statements (list): SQL语句列表
        table_name (str): 表名

    Returns:
        bool: 是否成功创建
    """
    if parent_path in dept_map:
        return True

    # 解析父级路径的父级
    grandparent_path = parse_parent_path_from_full_path(parent_path)

    # 递归确保祖父级存在
    if grandparent_path is not None:
        if not create_missing_parent_dept(grandparent_path, dept_map, child_counter,
                                        dept_order_counters, sql_statements, table_name):
            return False

    # 现在创建当前父级部门
    current_name = parent_path.split('>')[-1].strip()
    current_id = str(uuid.uuid4())

    # 确定父级信息
    if grandparent_path is None:
        # 根级别部门，父级为ROOT
        grandparent_code = 'ROOT'
        grandparent_id = 'ROOT'
    else:
        grandparent_info = dept_map.get(grandparent_path)
        if not grandparent_info:
            return False
        grandparent_code = grandparent_info['code']
        grandparent_id = grandparent_info['id']

    # 生成部门code
    current_code = generate_dept_code(grandparent_code if grandparent_code != 'ROOT' else None, child_counter)

    # 生成order_no
    order_no = get_order_no(grandparent_path, dept_order_counters)

    # 插入新部门的映射关系
    dept_map[parent_path] = {
        'id': current_id,
        'code': current_code,
        'pid': grandparent_id
    }

    # 获取当前时间
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 生成SQL语句
    sql = (
        f"INSERT INTO `{table_name}` (`id`, `pid`, `name`, `code`, `leader`, "
        f"`type`, `source`, `status`, `sort`, `createTime`, `createPeople`, "
        f"`bpmStatus`, `orgType`, `orderNo`, `updateTime`, "
        f"`updatePeople`) VALUES "
        f"('{current_id}', '{grandparent_id}', '{current_name}', "
        f"'{current_code}', NULL, 'UM', 'UC', 1, 1, '{now}', "
        f"'系统导入', NULL, NULL, {order_no}, NULL, NULL);"
    )

    sql_statements.append(sql)
    print(f"  自动创建缺失父级: {parent_path} (code: {current_code})")
    return True


def generate_insert_script(excel_file_path, table_name='sys_dept'):
    """
    读取organizations.xlsx文件并生成SQL INSERT脚本。
    根据full_path列的层级关系生成父子关系和code。

    Args:
        excel_file_path (str): Excel文件的路径。
        table_name (str): 目标表的名称。

    Returns:
        str: 包含所有INSERT语句的字符串。
    """
    try:
        # 读取Excel文件，使用第一行作为表头
        df = pd.read_excel(excel_file_path, header=0)
    except FileNotFoundError:
        print(f"错误: 找不到文件 {excel_file_path}")
        return ""
    except Exception as e:
        print(f"读取Excel文件时发生错误: {e}")
        return ""

    print(f"读取到 {len(df)} 条记录")

    # 用于存储所有部门的映射关系: {full_path: {'id': ..., 'code': 'pid': ...}}
    dept_map = {}
    # 用于存储每个父部门下的子部门code序号: {父部门code: 序号}
    child_counter = {}
    # 用于存储每个父级路径下的子部门order_no序号: {父级路径: 序号}
    dept_order_counters = {}
    # 存储所有生成的SQL语句
    sql_statements = []

    # 按grade排序，确保父部门先于子部门处理
    df_sorted = df.sort_values(['grade', 'full_path'])

    print("开始处理部门数据...")

    for index, row in df_sorted.iterrows():
        full_path = str(row['full_path']).strip()
        current_name = str(row['name']).strip()

        # 从full_path解析父级路径
        parent_path = parse_parent_path_from_full_path(full_path)

        print(f"处理: {full_path} (父级: {parent_path})")

        # 检查当前部门是否已处理过
        if full_path in dept_map:
            print(f"  跳过，已处理过")
            continue

        # 确保父级部门存在，如果不存在则自动创建
        if parent_path is not None and parent_path not in dept_map:
            if not create_missing_parent_dept(parent_path, dept_map, child_counter,
                                            dept_order_counters, sql_statements, table_name):
                print(f"  错误: 无法创建父级部门 {parent_path}")
                continue

        # 生成新的ID
        current_id = str(uuid.uuid4())

        # 确定父级信息
        if parent_path is None:
            # 根级别部门，父级为ROOT
            parent_code = 'ROOT'
            parent_id = 'ROOT'
        else:
            # 获取父级部门信息
            parent_info = dept_map[parent_path]
            parent_code = parent_info['code']
            parent_id = parent_info['id']

        # 生成部门code
        current_code = generate_dept_code(parent_code if parent_code != 'ROOT' else None, child_counter)

        # 生成order_no
        order_no = get_order_no(parent_path, dept_order_counters)

        # 插入新部门的映射关系
        dept_map[full_path] = {
            'id': current_id,
            'code': current_code,
            'pid': parent_id
        }

        # 获取当前时间
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 生成SQL语句
        sql = (
            f"INSERT INTO `{table_name}` (`id`, `pid`, `name`, `code`, `leader`, "
            f"`type`, `source`, `status`, `sort`, `createTime`, `createPeople`, "
            f"`bpmStatus`, `orgType`, `orderNo`, `updateTime`, "
            f"`updatePeople`) VALUES "
            f"('{current_id}', '{parent_id}', '{current_name}', "
            f"'{current_code}', NULL, 'UM', 'UC', 1, 1, '{now}', "
            f"'系统导入', NULL, NULL, {order_no}, NULL, NULL);"
        )

        sql_statements.append(sql)
        print(f"  生成: code={current_code}, order_no={order_no}")

    print(f"总共处理了 {len(sql_statements)} 条记录")
    return "\n".join(sql_statements)


if __name__ == '__main__':
    excel_file = 'organizations.xlsx'
    sql_content = generate_insert_script(excel_file)

    if sql_content:
        # 写入SQL文件
        output_file = 'insert_data.sql'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(sql_content)
        print(f"成功生成SQL脚本文件: {output_file}")
        print("您现在可以使用此文件将数据导入到MySQL中。")
    else:
        print("未能生成SQL内容，请检查Excel文件是否存在且格式正确。")
